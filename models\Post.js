const mongoose = require('mongoose');

const PostSchema = new mongoose.Schema({
  content: {
    type: String,
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  toxicity: {
    type: String,
    enum: ['toxic', 'non-toxic'],
    required: true
  },
  sentiment: {
    type: String,
    enum: ['very negative','very positive','positive', 'negative', 'neutral'],
    required: true
  },
  finalDecision: {
    type: String,
    enum: ['allowed', 'flagged', 'removed'],
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Post', PostSchema);